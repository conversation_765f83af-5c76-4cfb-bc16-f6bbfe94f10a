// Copyright (c) 2025

import {expect} from '@loopback/testlab';
import sinon from 'sinon';
import {Request} from '@loopback/rest';
import {BillingInvoiceController} from '../../controllers/invoice.controller';
import {SubscriptionProxyService} from '../../services/proxies';
import {Invoice} from '../../models';

describe('BillingInvoiceController (unit)', () => {
  let subscriptionProxyService: sinon.SinonStubbedInstance<SubscriptionProxyService>;
  let request: Partial<Request>;
  let controller: BillingInvoiceController;

  beforeEach(() => {
    subscriptionProxyService = {
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
    } as any;
    request = {
      headers: {
        authorization: 'Bearer test-token',
      },
    };
    controller = new BillingInvoiceController(
      subscriptionProxyService as any,
      request as Request,
    );
  });

  it('calls getInvoices with token and filter', async () => {
    const filter = {where: {amount: 1000}};
    const invoices = [
      new Invoice({id: '1', amount: 1000}),
      new Invoice({id: '2', amount: 1000}),
    ];
    (subscriptionProxyService.getInvoices as sinon.SinonStub).resolves(invoices);

    const result = await controller.find(filter);
    expect(subscriptionProxyService.getInvoices.calledOnce).to.be.true();
    expect(subscriptionProxyService.getInvoices.firstCall.args[0]).to.equal('Bearer test-token');
    expect(subscriptionProxyService.getInvoices.firstCall.args[1]).to.eql(filter);
    expect(result).to.eql(invoices);
  });

  it('calls getInvoicesCount with token and where', async () => {
    const where = {amount: 1000};
    const count = {count: 2};
    (subscriptionProxyService.getInvoicesCount as sinon.SinonStub).resolves(count);

    const result = await controller.count(where);
    expect(subscriptionProxyService.getInvoicesCount.calledOnce).to.be.true();
    expect(subscriptionProxyService.getInvoicesCount.firstCall.args[0]).to.equal('Bearer test-token');
    expect(subscriptionProxyService.getInvoicesCount.firstCall.args[1]).to.eql(where);
    expect(result).to.eql(count);
  });

  it('handles missing authorization header in find method', async () => {
    request.headers = {}; // No authorization header
    controller = new BillingInvoiceController(
      subscriptionProxyService as any,
      request as Request,
    );

    const filter = {where: {amount: 1000}};
    const invoices = [new Invoice({id: '1', amount: 1000})];
    (subscriptionProxyService.getInvoices as sinon.SinonStub).resolves(invoices);

    const result = await controller.find(filter);
    expect(subscriptionProxyService.getInvoices.calledOnce).to.be.true();
    expect(subscriptionProxyService.getInvoices.firstCall.args[0]).to.equal('');
    expect(subscriptionProxyService.getInvoices.firstCall.args[1]).to.eql(filter);
    expect(result).to.eql(invoices);
  });

  it('handles missing authorization header in count method', async () => {
    request.headers = {}; // No authorization header
    controller = new BillingInvoiceController(
      subscriptionProxyService as any,
      request as Request,
    );

    const where = {amount: 1000};
    const count = {count: 0};
    (subscriptionProxyService.getInvoicesCount as sinon.SinonStub).resolves(count);

    const result = await controller.count(where);
    expect(subscriptionProxyService.getInvoicesCount.calledOnce).to.be.true();
    expect(subscriptionProxyService.getInvoicesCount.firstCall.args[0]).to.equal('');
    expect(subscriptionProxyService.getInvoicesCount.firstCall.args[1]).to.eql(where);
    expect(result).to.eql(count);
  });

  it('calls getInvoices with undefined filter', async () => {
    const invoices = [new Invoice({id: '1', amount: 1000})];
    (subscriptionProxyService.getInvoices as sinon.SinonStub).resolves(invoices);

    const result = await controller.find(undefined);
    expect(subscriptionProxyService.getInvoices.calledOnce).to.be.true();
    expect(subscriptionProxyService.getInvoices.firstCall.args[0]).to.equal('Bearer test-token');
    expect(subscriptionProxyService.getInvoices.firstCall.args[1]).to.be.undefined();
    expect(result).to.eql(invoices);
  });

  it('calls getInvoicesCount with undefined where', async () => {
    const count = {count: 10};
    (subscriptionProxyService.getInvoicesCount as sinon.SinonStub).resolves(count);

    const result = await controller.count(undefined);
    expect(subscriptionProxyService.getInvoicesCount.calledOnce).to.be.true();
    expect(subscriptionProxyService.getInvoicesCount.firstCall.args[0]).to.equal('Bearer test-token');
    expect(subscriptionProxyService.getInvoicesCount.firstCall.args[1]).to.be.undefined();
    expect(result).to.eql(count);
  });

  it('handles complex filter with multiple conditions', async () => {
    const complexFilter = {
      where: {amount: {gte: 1000}},
      order: ['createdOn DESC'],
      limit: 10
    };
    const invoices = [
      new Invoice({id: '1', amount: 1500}),
      new Invoice({id: '2', amount: 2000})
    ];
    (subscriptionProxyService.getInvoices as sinon.SinonStub).resolves(invoices);

    const result = await controller.find(complexFilter);
    expect(subscriptionProxyService.getInvoices.calledOnce).to.be.true();
    expect(subscriptionProxyService.getInvoices.firstCall.args[0]).to.equal('Bearer test-token');
    expect(subscriptionProxyService.getInvoices.firstCall.args[1]).to.eql(complexFilter);
    expect(result).to.eql(invoices);
  });

  it('handles complex where clause with multiple conditions', async () => {
    const complexWhere = {amount: {gte: 1000}};
    const count = {count: 25};
    (subscriptionProxyService.getInvoicesCount as sinon.SinonStub).resolves(count);

    const result = await controller.count(complexWhere);
    expect(subscriptionProxyService.getInvoicesCount.calledOnce).to.be.true();
    expect(subscriptionProxyService.getInvoicesCount.firstCall.args[0]).to.equal('Bearer test-token');
    expect(subscriptionProxyService.getInvoicesCount.firstCall.args[1]).to.eql(complexWhere);
    expect(result).to.eql(count);
  });
});