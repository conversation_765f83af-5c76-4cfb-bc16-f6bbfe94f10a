// Copyright (c) 2025

import {expect} from '@loopback/testlab';
import sinon from 'sinon';
import {Request} from '@loopback/rest';
import {BillingInvoiceController} from '../../controllers/invoice.controller';
import {SubscriptionProxyService} from '../../services/proxies';
import {Invoice} from '../../models';

describe('BillingInvoiceController (unit)', () => {
  let subscriptionProxyService: sinon.SinonStubbedInstance<SubscriptionProxyService>;
  let request: Partial<Request>;
  let controller: BillingInvoiceController;

  beforeEach(() => {
    subscriptionProxyService = {
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
    } as any;
    request = {
      headers: {
        authorization: 'Bearer test-token',
      },
    };
    controller = new BillingInvoiceController(
      subscriptionProxyService as any,
      request as Request,
    );
  });

  it('calls getInvoices with token and filter', async () => {
    const filter = {where: {amount: 1000}};
    const invoices = [
      new Invoice({id: '1', amount: 1000}),
      new Invoice({id: '2', amount: 1000}),
    ];
    (subscriptionProxyService.getInvoices as sinon.SinonStub).resolves(invoices);

    const result = await controller.find(filter);
    expect(subscriptionProxyService.getInvoices.calledOnce).to.be.true();
    expect(subscriptionProxyService.getInvoices.firstCall.args[0]).to.equal('Bearer test-token');
    expect(subscriptionProxyService.getInvoices.firstCall.args[1]).to.eql(filter);
    expect(result).to.eql(invoices);
  });

  it('calls getInvoicesCount with token and where', async () => {
    const where = {amount: 1000};
    const count = {count: 2};
    (subscriptionProxyService.getInvoicesCount as sinon.SinonStub).resolves(count);

    const result = await controller.count(where);
    expect(subscriptionProxyService.getInvoicesCount.calledOnce).to.be.true();
    expect(subscriptionProxyService.getInvoicesCount.firstCall.args[0]).to.equal('Bearer test-token');
    expect(subscriptionProxyService.getInvoicesCount.firstCall.args[1]).to.eql(where);
    expect(result).to.eql(count);
  });
});